<script lang="ts">
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import {
		invoiceSchema,
		invoiceModalSchema,
		accountingPeriods,
		type InvoiceSchema,
		type InvoiceModalSchema,
		type InvoiceListItem,
	} from '$lib/schemas/invoice';
	import type { VendorListItem, VendorSchema } from '$lib/schemas/vendor';
	import type { PurchaseOrderListItem } from '$lib/schemas/purchase_order';
	import * as Form from '$lib/components/ui/form';
	import * as Select from '$lib/components/ui/select';
	import * as Calendar from '$lib/components/ui/calendar';
	import * as Popover from '$lib/components/ui/popover';

	import * as Command from '$lib/components/ui/command';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { tick } from 'svelte';
	import ChevronsUpDownIcon from 'phosphor-svelte/lib/CaretUpDown';
	import CheckIcon from 'phosphor-svelte/lib/Check';
	import { toast } from 'svelte-sonner';
	import CalendarIcon from 'phosphor-svelte/lib/Calendar';
	import {
		DateFormatter,
		getLocalTimeZone,
		parseDate,
		today,
		type DateValue,
	} from '@internationalized/date';
	import { cn } from '$lib/utils.js';

	const {
		data,
		isModal = false,
		onInvoiceCreated,
	}: {
		data: {
			form: SuperValidated<InvoiceSchema | InvoiceModalSchema>;
			newVendorForm?: SuperValidated<VendorSchema>;
			vendors: VendorListItem[];
			purchaseOrders: PurchaseOrderListItem[];
			project: {
				project_id: string;
				name: string;
				client: {
					client_id: string;
					name: string;
					organization: {
						org_id: string;
						name: string;
					};
				};
			};
		};
		isModal?: boolean;
		onInvoiceCreated?: (invoice: InvoiceListItem | null) => void;
	} = $props();

	const { vendors, purchaseOrders } = data;

	const form = superForm(data.form, {
		validators: zodClient(isModal ? invoiceModalSchema : invoiceSchema),
		onResult({ result }) {
			if (result.type === 'success' && result.data) {
				// Handle modal response with invoice data
				if (isModal && onInvoiceCreated && 'invoice' in result.data) {
					const invoice = result.data.invoice as InvoiceListItem;
					onInvoiceCreated(invoice);
				}
			}
		},
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance } = form;

	let vendorList = $state<VendorListItem[]>([...vendors] as VendorListItem[]);

	// Date handling
	const df = new DateFormatter('default', {
		dateStyle: 'long',
	});

	let invoiceDateValue = $derived(
		$formData.invoice_date ? parseDate($formData.invoice_date) : undefined,
	);
	let postDateValue = $derived($formData.post_date ? parseDate($formData.post_date) : undefined);
	let placeholder = $state<DateValue>(today(getLocalTimeZone()));
	let invoiceDateCalendarOpen = $state(false);
	let postDateCalendarOpen = $state(false);

	// Get selected purchase order and its vendor
	const selectedPurchaseOrder = $derived(
		purchaseOrders.find((po) => po.purchase_order_id === $formData.purchase_order_id),
	);
	const selectedVendor = $derived(
		selectedPurchaseOrder
			? vendorList.find((v) => v.vendor_id === selectedPurchaseOrder.vendor_id)
			: null,
	);

	// Purchase order options for combobox
	const purchaseOrderOptions = $derived(
		purchaseOrders.map((po) => ({
			value: po.purchase_order_id,
			label: po.vendor_name ? `${po.po_number} - ${po.vendor_name}` : po.po_number,
		})),
	);

	// Combobox state
	let purchaseOrderOpen = $state(false);
	const purchaseOrderTriggerId = 'purchase-order-trigger';

	// Helper function to close combobox and refocus trigger
	function closeAndFocusTrigger(triggerId: string, setOpen: (value: boolean) => void) {
		setOpen(false);
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}

	// Handle PO selection
	function handlePOSelection(purchaseOrderId: string) {
		$formData.purchase_order_id = purchaseOrderId;
	}
</script>

<div class="space-y-6">
	<div class="rounded-lg border p-6 shadow-sm">
		<form method="POST" use:enhance action={isModal ? '?/createInvoiceModal' : '?/createInvoice'}>
			<div class="space-y-6">
				<!-- Basic Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Invoice Information</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<!-- Purchase Order -->
						<Form.Field {form} name="purchase_order_id">
							<Popover.Root bind:open={purchaseOrderOpen}>
								<Form.Control id={purchaseOrderTriggerId}>
									{#snippet children({ props })}
										<Form.Label>Purchase Order <span class="text-red-500">*</span></Form.Label>
										<Popover.Trigger
											class={cn(
												buttonVariants({ variant: 'outline' }),
												'w-full justify-between',
												!$formData.purchase_order_id && 'text-muted-foreground',
											)}
											role="combobox"
											{...props}
										>
											{$formData.purchase_order_id
												? purchaseOrderOptions.find((f) => f.value === $formData.purchase_order_id)
														?.label
												: 'Select a purchase order'}
											<ChevronsUpDownIcon class="opacity-50" />
										</Popover.Trigger>
										<input hidden value={$formData.purchase_order_id} name={props.name} />
									{/snippet}
								</Form.Control>
								<Popover.Content class="w-full p-0">
									<Command.Root>
										<Command.Input autofocus placeholder="Search purchase orders..." class="h-9" />
										<Command.Empty>No purchase order found.</Command.Empty>
										<Command.Group class="max-h-[300px] overflow-y-auto">
											{#each purchaseOrderOptions as option (option.value)}
												<Command.Item
													value={option.label}
													onSelect={() => {
														handlePOSelection(option.value);
														closeAndFocusTrigger(
															purchaseOrderTriggerId,
															(value) => (purchaseOrderOpen = value),
														);
													}}
												>
													{option.label}
													<CheckIcon
														class={cn(
															'ml-auto',
															option.value !== $formData.purchase_order_id && 'text-transparent',
														)}
													/>
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.Root>
								</Popover.Content>
							</Popover.Root>
							<Form.FieldErrors />
						</Form.Field>

						<!-- Vendor (read-only, from purchase order) -->
						<div class="space-y-2">
							<span
								class="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
								>Vendor</span
							>
							<div
								class="border-input bg-muted flex h-10 w-full rounded-md border px-3 py-2 text-sm"
							>
								{selectedVendor?.name || 'Select a purchase order to see vendor'}
							</div>
							<p class="text-muted-foreground text-xs">
								Vendor is automatically selected based on the purchase order
							</p>
						</div>
					</div>

					<!-- Description -->
					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description</Form.Label>
								<Textarea
									{...props}
									placeholder="Brief description of the invoice"
									bind:value={$formData.description}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Financial Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Financial Details</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<!-- Amount -->
						<Form.Field {form} name="amount">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Amount <span class="text-red-500">*</span></Form.Label>
									<Input
										{...props}
										type="number"
										step="0.01"
										min="0"
										placeholder="0.00"
										bind:value={$formData.amount}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<!-- Account -->
						<Form.Field {form} name="account">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Account <span class="text-red-500">*</span></Form.Label>
									<Input
										{...props}
										type="text"
										placeholder="Enter account code"
										bind:value={$formData.account}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<!-- Period -->
					<Form.Field {form} name="period">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Accounting Period</Form.Label>
								<Select.Root
									type="single"
									value={$formData.period || undefined}
									onValueChange={(value) => {
										$formData.period = value || null;
									}}
									name={props.name}
								>
									<Select.Trigger {...props}>
										{$formData.period || 'Select period'}
									</Select.Trigger>
									<Select.Content>
										{#each accountingPeriods as period (period)}
											<Select.Item value={period}>
												{period}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Date Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Date Information</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<!-- Invoice Date -->
						<Form.Field {form} name="invoice_date">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Invoice Date <span class="text-red-500">*</span></Form.Label>
									<Popover.Root bind:open={invoiceDateCalendarOpen}>
										<Popover.Trigger>
											{#snippet child({ props: triggerProps })}
												<Button
													{...props}
													{...triggerProps}
													variant="outline"
													class="w-full justify-start text-left font-normal"
												>
													<CalendarIcon class="mr-2 h-4 w-4" />
													{invoiceDateValue
														? df.format(invoiceDateValue.toDate(getLocalTimeZone()))
														: 'Pick invoice date'}
												</Button>
											{/snippet}
										</Popover.Trigger>
										<Popover.Content class="w-auto p-0" align="start">
											<Calendar.Calendar
												type="single"
												value={invoiceDateValue as DateValue}
												bind:placeholder
												captionLayout="dropdown"
												onValueChange={(v) => {
													if (v) {
														$formData.invoice_date = v.toString();
													} else {
														$formData.invoice_date = '';
													}
													invoiceDateCalendarOpen = false;
												}}
											/>
										</Popover.Content>
									</Popover.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<!-- Post Date -->
						<Form.Field {form} name="post_date">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Post Date <span class="text-red-500">*</span></Form.Label>
									<Popover.Root bind:open={postDateCalendarOpen}>
										<Popover.Trigger>
											{#snippet child({ props: triggerProps })}
												<Button
													{...props}
													{...triggerProps}
													variant="outline"
													class="w-full justify-start text-left font-normal"
												>
													<CalendarIcon class="mr-2 h-4 w-4" />
													{postDateValue
														? df.format(postDateValue.toDate(getLocalTimeZone()))
														: 'Pick post date'}
												</Button>
											{/snippet}
										</Popover.Trigger>
										<Popover.Content class="w-auto p-0" align="start">
											<Calendar.Calendar
												type="single"
												value={postDateValue as DateValue}
												bind:placeholder
												captionLayout="dropdown"
												onValueChange={(v) => {
													if (v) {
														$formData.post_date = v.toString();
													} else {
														$formData.post_date = '';
													}
													postDateCalendarOpen = false;
												}}
											/>
										</Popover.Content>
									</Popover.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>
				</div>

				<!-- Notes -->
				<Form.Field {form} name="notes">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Notes</Form.Label>
							<Textarea {...props} bind:value={$formData.notes} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Actions -->
				<div class="flex gap-4">
					<Button type="submit">
						{isModal ? 'Create Invoice' : 'Create Invoice'}
					</Button>
					{#if isModal && onInvoiceCreated}
						<Button type="button" variant="outline" onclick={() => onInvoiceCreated?.(null)}>
							Cancel
						</Button>
					{/if}
				</div>
			</div>
		</form>
	</div>
</div>
