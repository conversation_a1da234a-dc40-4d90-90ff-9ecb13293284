# Accounting Period Format Upgrade

## Overview

The invoice accounting period field has been upgraded from a simple month name format (e.g., "January") to a standardized YYYY-MM format (e.g., "2024-01"). This change ensures data consistency and enables better filtering, sorting, and reporting capabilities.

## Changes Made

### 1. Database Schema Updates

- **Column Comment**: Updated to reflect the new YYYY-MM format
- **Check Constraint**: Added `invoice_period_format_check` to ensure periods follow the `^\d{4}-\d{2}$` pattern
- **Data Migration**: Existing month-only periods are automatically converted to current year format

### 2. TypeScript Schema Updates

- **Zod Validation**: All invoice schemas now validate period format with regex
- **Helper Functions**: Added utility functions for period manipulation and formatting

### 3. UI Component Updates

- **Form Components**: Both create and edit invoice forms now display formatted periods
- **List Display**: Invoice tables show periods in human-readable format (e.g., "January 2024")
- **Default Values**: New invoices default to the current month/year

## New Utility Functions

### Core Functions

```typescript
// Generate periods for dropdown (2 years back, 2 years forward)
generateAccountingPeriods(yearsBack?: number, yearsForward?: number): string[]

// Format period for display: "2024-01" → "January 2024"
formatAccountingPeriod(period: string): string

// Get current period in YYYY-MM format
getCurrentAccountingPeriod(): string

// Parse period into year and month components
parseAccountingPeriod(period: string): { year: number; month: number } | null
```

### Filtering Helpers

```typescript
// Get all periods for a specific year
getPeriodsForYear(year: number): string[]

// Extract unique years from a list of periods
getYearsFromPeriods(periods: (string | null)[]): number[]
```

## Benefits

### 1. Data Consistency
- **Unambiguous**: Each period clearly identifies month and year
- **Sortable**: Periods can be sorted chronologically
- **Filterable**: Easy to filter by specific month/year combinations

### 2. Better User Experience
- **Clear Display**: Users see "January 2024" instead of just "January"
- **Smart Defaults**: New invoices default to current month
- **Validation**: Prevents invalid period entries

### 3. Enhanced Reporting
- **Multi-year Support**: Can handle invoices across multiple years
- **Period Comparisons**: Easy to compare same months across different years
- **Chronological Ordering**: Proper sorting for financial reports

## Usage Examples

### Creating Invoices
```typescript
// The form automatically defaults to current period
const currentPeriod = getCurrentAccountingPeriod(); // "2024-07"

// Users see formatted display in dropdown
formatAccountingPeriod("2024-01"); // "January 2024"
```

### Filtering Invoices
```typescript
// Filter by specific year
const periodsFor2024 = getPeriodsForYear(2024);
// ["2024-01", "2024-02", ..., "2024-12"]

// Get years from existing data
const years = getYearsFromPeriods(invoices.map(i => i.period));
// [2024, 2023, 2022] (sorted newest first)
```

### Display in Tables
```typescript
// Show formatted period in UI
{invoice.period ? formatAccountingPeriod(invoice.period) : '—'}
```

## Migration Details

The migration automatically converts existing data:

- **"January"** → **"2024-01"** (using current year)
- **"February"** → **"2024-02"**
- **null** → **null** (unchanged)

No manual intervention required for existing data.

## Database Constraint

The new constraint ensures data integrity:

```sql
ALTER TABLE "public"."invoice" 
ADD CONSTRAINT "invoice_period_format_check" 
CHECK (period IS NULL OR period ~ '^\d{4}-\d{2}$');
```

This prevents invalid formats like:
- ❌ "January"
- ❌ "2024-1" (missing leading zero)
- ❌ "24-01" (2-digit year)
- ✅ "2024-01" (correct format)
- ✅ null (allowed)

## Future Enhancements

With this foundation, you can now easily add:

1. **Period-based Filtering**: Filter invoices by specific months/years
2. **Financial Reports**: Generate reports by accounting period
3. **Period Comparisons**: Compare performance across periods
4. **Bulk Operations**: Process invoices by period
5. **Dashboard Widgets**: Show period-based metrics

## Example Filter Component

A sample filter component (`InvoiceFilters.svelte`) has been created to demonstrate how to implement period-based filtering with the new format.
